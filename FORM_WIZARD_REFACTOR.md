# Scout Job Posting Form - Modular Architecture Refactor

## Overview

This document outlines the refactored Scout job posting form architecture that implements a more modular, maintainable, and extensible system for multi-step form management.

## Problems Solved

The original implementation had several issues:

1. **Tightly coupled branching logic** - Hard-coded step sequences in JavaScript
2. **Monolithic form view** - All steps and categories in one large file
3. **Inconsistent validation** - Validation logic scattered across multiple files
4. **Difficult maintenance** - Adding new categories required changes in multiple places
5. **Poor separation of concerns** - Business logic, presentation, and state management mixed

## New Architecture

### 1. Form Configuration System (`app/javascript/form_wizard/form_config.js`)

Centralized configuration that defines:
- Form categories and their step sequences
- Step definitions with validation requirements
- Conditional field logic
- Progress calculation

**Benefits:**
- Easy to add/remove/modify categories and steps
- Single source of truth for form structure
- Declarative configuration approach

### 2. Modular Step Components (`app/views/scout/jobs/steps/`)

Broken down into reusable step partials:
- `_category_selection.html.erb` - Initial category selection
- `_newsletter_steps.html.erb` - Newsletter-specific steps
- `_social_media_steps.html.erb` - Social media-specific steps
- `_lead_magnet_steps.html.erb` - Lead magnet-specific steps
- `_job_details.html.erb` - Common job details steps

**Benefits:**
- Easier to maintain individual steps
- Reusable components
- Clear separation of concerns
- Easier testing and debugging

### 3. Enhanced State Management (`app/javascript/form_wizard/step_manager.js`)

Robust state manager that handles:
- Step navigation and progression
- Form state persistence
- UI updates and synchronization
- Event handling and callbacks

**Benefits:**
- Centralized state management
- Consistent behavior across all steps
- Easy to extend with new features
- Better error handling

### 4. Unified Validation System (`app/javascript/form_wizard/validator.js`)

Comprehensive validation framework:
- Field-level validation
- Conditional validation based on other fields
- Consistent error display
- Real-time validation feedback

**Benefits:**
- Consistent validation across all steps
- Easy to add new validation rules
- Better user experience
- Reduced form submission errors

### 5. Main Form Wizard Controller (`app/javascript/controllers/form_wizard_controller.js`)

Modern Stimulus controller that:
- Orchestrates the entire form wizard
- Provides clean API for external interaction
- Emits events for integration with other components
- Handles form submission and error states

**Benefits:**
- Clean separation from business logic
- Easy to integrate with other systems
- Extensible through events
- Modern JavaScript patterns

### 6. Server-side Configuration (`app/models/concerns/job_form_configuration.rb`)

Ruby concern that provides:
- Server-side form configuration
- Step validation helpers
- Progress calculation methods
- JSON export for JavaScript integration

**Benefits:**
- Consistent configuration between client and server
- Server-side validation support
- Easy integration with Rails models
- Type safety and validation

## File Structure

```
app/
├── javascript/
│   ├── controllers/
│   │   └── form_wizard_controller.js          # Main Stimulus controller
│   └── form_wizard/
│       ├── form_config.js                     # Form configuration
│       ├── step_manager.js                    # State management
│       └── validator.js                       # Validation system
├── models/
│   ├── concerns/
│   │   └── job_form_configuration.rb          # Server-side config
│   └── job.rb                                 # Updated Job model
└── views/
    └── scout/
        └── jobs/
            ├── _form.html.erb                 # Simplified main form
            ├── new.html.erb                   # Updated to use new controller
            └── steps/                         # Step components
                ├── _category_selection.html.erb
                ├── _newsletter_steps.html.erb
                ├── _social_media_steps.html.erb
                ├── _lead_magnet_steps.html.erb
                └── _job_details.html.erb
```

## Usage Examples

### Adding a New Category

1. **Update form configuration:**
```javascript
// In app/javascript/form_wizard/form_config.js
export const FORM_CATEGORIES = {
  // ... existing categories
  blog_writing: {
    name: 'Blog Writing',
    steps: ['topic', 'frequency', 'length']
  }
};
```

2. **Add step definitions:**
```javascript
// In app/javascript/form_wizard/form_config.js
export const STEP_DEFINITIONS = {
  // ... existing steps
  'blog_writing.topic': {
    name: 'Blog Topic',
    title: 'Blog Topic Selection',
    category: 'blog_writing',
    required: true,
    validation: {
      fields: ['blog_topic']
    }
  }
  // ... more steps
};
```

3. **Create step partial:**
```erb
<!-- app/views/scout/jobs/steps/_blog_writing_steps.html.erb -->
<div data-section-name="blog_writing" class="hidden">
  <!-- Step content -->
</div>
```

4. **Include in main form:**
```erb
<!-- app/views/scout/jobs/_form.html.erb -->
<%= render 'scout/jobs/steps/blog_writing_steps', form: form %>
```

### Adding Custom Validation

```javascript
// In step definition
validation: {
  fields: ['required_field'],
  conditional: {
    field: 'trigger_field',
    values: ['specific_value'],
    requiredFields: ['conditional_field']
  }
}
```

### Handling Form Events

```javascript
// Listen for form wizard events
document.addEventListener('form-wizard:stepChanged', (event) => {
  console.log('Step changed:', event.detail);
});

document.addEventListener('form-wizard:categoryChanged', (event) => {
  console.log('Category changed:', event.detail);
});
```

## Migration Guide

### From Old System

1. **Replace controller reference:**
   ```erb
   <!-- Old -->
   data: { controller: "form-branching" }
   
   <!-- New -->
   data: { controller: "form-wizard" }
   ```

2. **Update action references:**
   ```erb
   <!-- Old -->
   data: { action: "change->form-branching#showHideSections" }
   
   <!-- New -->
   data: { action: "change->form-wizard#handleCategoryChange" }
   ```

3. **Update target references:**
   ```erb
   <!-- Old -->
   data-form-branching-target="nextButton"
   
   <!-- New -->
   data-form-wizard-target="nextButton"
   ```

### Backward Compatibility

The old `form_branching_controller.js` is marked as deprecated but still functional to ensure smooth transition. It will be removed in a future release.

## Testing

### JavaScript Testing

```javascript
// Example test for step manager
import { StepManager } from '../app/javascript/form_wizard/step_manager.js';

test('should navigate to next step', () => {
  const formElement = document.createElement('form');
  const stepManager = new StepManager(formElement);
  
  expect(stepManager.nextStep()).toBe(true);
  expect(stepManager.getCurrentStepIndex()).toBe(1);
});
```

### Rails Testing

```ruby
# Example test for form configuration
require 'rails_helper'

RSpec.describe Job, type: :model do
  describe 'form configuration' do
    it 'returns correct step sequence for newsletter category' do
      job = Job.new(job_category: 'newsletter')
      sequence = job.step_sequence
      
      expect(sequence).to include('newsletter.goal', 'newsletter.frequency')
    end
  end
end
```

## Performance Considerations

1. **Lazy Loading** - Step components are only rendered when needed
2. **Event Delegation** - Efficient event handling for dynamic content
3. **Minimal DOM Manipulation** - Only update what's necessary
4. **Caching** - Configuration is cached to avoid repeated calculations

## Future Enhancements

1. **Step Persistence** - Save progress to localStorage or server
2. **Dynamic Step Loading** - Load step content via AJAX
3. **Advanced Validation** - Server-side validation integration
4. **Analytics Integration** - Track step completion and abandonment
5. **A/B Testing** - Support for different form flows
6. **Accessibility** - Enhanced screen reader support and keyboard navigation

## Conclusion

This refactored architecture provides a solid foundation for maintaining and extending the Scout job posting form. The modular approach makes it easy to add new categories, modify existing steps, and maintain consistent behavior across the entire form wizard.
