// Form Wizard Controller
// Modern, modular replacement for form_branching_controller.js

import { Controller } from "@hotwired/stimulus";
import { StepManager } from "../form_wizard/step_manager.js";

export default class extends Controller {
  static targets = [
    "step",
    "nextButton",
    "previousButton",
    "submitButton",
    "progressBar",
    "stepTitle",
    "devPreview",
  ];

  static values = {
    currentStep: { type: Number, default: 0 },
    validateOnNext: { type: Boolean, default: true },
    showProgress: { type: Boolean, default: true },
  };

  connect() {
    console.log("FormWizardController connected");
    console.log("Form element:", this.element);
    console.log("Available targets:", this.targets);
    this.initializeStepManager();
    this.setupDevPreviewListeners();
    this.initializeCategoryCardSelection();
    this.initializeGoalCardSelection();
    this.setupVerticalProgressListener();
  }

  disconnect() {
    if (this.stepManager) {
      // Clean up any event listeners or resources
      this.stepManager = null;
    }

    // Remove vertical progress listener
    if (this.boundHandleVerticalProgressNavigation) {
      this.element.removeEventListener(
        "vertical-progress:navigate",
        this.boundHandleVerticalProgressNavigation
      );
    }
  }

  setupVerticalProgressListener() {
    // Bind the handler to preserve 'this' context
    this.boundHandleVerticalProgressNavigation =
      this.handleVerticalProgressNavigation.bind(this);

    // Listen for navigation events from the vertical progress stepper
    this.element.addEventListener(
      "vertical-progress:navigate",
      this.boundHandleVerticalProgressNavigation
    );
  }

  handleVerticalProgressNavigation(event) {
    const { targetStep } = event.detail;
    console.log("FormWizard: Received navigation request to step", targetStep);

    if (this.stepManager && this.stepManager.goToStep(targetStep)) {
      this.currentStepValue = this.stepManager.getCurrentStepIndex();

      this.dispatch("stepChanged", {
        detail: {
          currentStep: this.currentStepValue,
          stepKey: this.stepManager.getCurrentStepKey(),
          stepDefinition: this.stepManager.getCurrentStepDefinition(),
          totalSteps: this.stepManager.getTotalSteps(),
        },
      });

      // Also dispatch a global event for the vertical progress stepper
      this.dispatchGlobalEvent("form-wizard:stepChanged", {
        currentStep: this.currentStepValue,
        stepKey: this.stepManager.getCurrentStepKey(),
        stepDefinition: this.stepManager.getCurrentStepDefinition(),
        totalSteps: this.stepManager.getTotalSteps(),
      });
    }
  }

  // Initialize the step manager with current configuration
  initializeStepManager() {
    const options = {
      validateOnNext: this.validateOnNextValue,
      showProgress: this.showProgressValue,
      autoHideSteps: true,
    };

    this.stepManager = new StepManager(this.element, options);

    // Set up event listeners for topic selection
    this.setupTopicEventListeners();

    // Sync initial state
    if (this.currentStepValue > 0) {
      this.stepManager.goToStep(this.currentStepValue);
    }
  }

  // Set up event listeners for topic selection to store topics when they change
  setupTopicEventListeners() {
    // Listen for changes to topic checkboxes
    const topicCheckboxes = this.element.querySelectorAll(
      'input[name="job[topics][]"]'
    );
    topicCheckboxes.forEach((checkbox) => {
      checkbox.addEventListener("change", () => {
        this.storeCurrentTopics();
      });
    });

    // Listen for changes to other topic field
    const otherTopicField = this.element.querySelector(
      'input[name="job[other_topic]"]'
    );
    if (otherTopicField) {
      otherTopicField.addEventListener("input", () => {
        this.storeCurrentTopics();
      });
    }
  }

  // Store current topics from form inputs
  storeCurrentTopics() {
    const checkedTopics = Array.from(
      this.element.querySelectorAll('input[name="job[topics][]"]:checked')
    ).map((input) => input.value);

    const otherTopicField = this.element.querySelector(
      'input[name="job[other_topic]"]'
    );
    const otherTopic = otherTopicField?.value?.trim();

    if (otherTopic) {
      checkedTopics.push(otherTopic);
    }

    this.storeSelectedTopics(checkedTopics);
  }

  // Navigation methods
  nextStep(event) {
    event?.preventDefault();

    if (this.stepManager.nextStep()) {
      this.currentStepValue = this.stepManager.getCurrentStepIndex();

      // Update topics display when entering newsletter or lead magnet steps
      const currentStepKey = this.stepManager.getCurrentStepKey();
      if (
        currentStepKey &&
        (currentStepKey.startsWith("newsletter_") ||
          currentStepKey.startsWith("lead_magnet_") ||
          currentStepKey === "lead_magnet_goal")
      ) {
        this.updateSelectedTopicsDisplay();
      }

      this.dispatch("stepChanged", {
        detail: {
          currentStep: this.currentStepValue,
          stepKey: this.stepManager.getCurrentStepKey(),
          stepDefinition: this.stepManager.getCurrentStepDefinition(),
          totalSteps: this.stepManager.getTotalSteps(),
        },
      });

      // Also dispatch a global event for the vertical progress stepper
      this.dispatchGlobalEvent("form-wizard:stepChanged", {
        currentStep: this.currentStepValue,
        stepKey: this.stepManager.getCurrentStepKey(),
        stepDefinition: this.stepManager.getCurrentStepDefinition(),
        totalSteps: this.stepManager.getTotalSteps(),
      });

      // Update development preview if available
      this.updateDevPreview();
    }
  }

  previousStep(event) {
    event?.preventDefault();

    if (this.stepManager.previousStep()) {
      this.currentStepValue = this.stepManager.getCurrentStepIndex();
      this.dispatch("stepChanged", {
        detail: {
          currentStep: this.currentStepValue,
          stepKey: this.stepManager.getCurrentStepKey(),
          stepDefinition: this.stepManager.getCurrentStepDefinition(),
          totalSteps: this.stepManager.getTotalSteps(),
        },
      });

      // Also dispatch a global event for the vertical progress stepper
      this.dispatchGlobalEvent("form-wizard:stepChanged", {
        currentStep: this.currentStepValue,
        stepKey: this.stepManager.getCurrentStepKey(),
        stepDefinition: this.stepManager.getCurrentStepDefinition(),
        totalSteps: this.stepManager.getTotalSteps(),
      });
    }
  }

  goToStep(event) {
    const targetStep = parseInt(event.currentTarget.dataset.step);
    if (isNaN(targetStep)) {
      console.warn("Invalid step number:", event.currentTarget.dataset.step);
      return;
    }

    if (this.stepManager.goToStep(targetStep)) {
      this.currentStepValue = this.stepManager.getCurrentStepIndex();
      this.dispatch("stepChanged", {
        detail: {
          currentStep: this.currentStepValue,
          stepKey: this.stepManager.getCurrentStepKey(),
          stepDefinition: this.stepManager.getCurrentStepDefinition(),
          totalSteps: this.stepManager.getTotalSteps(),
        },
      });

      // Also dispatch a global event for the vertical progress stepper
      this.dispatchGlobalEvent("form-wizard:stepChanged", {
        currentStep: this.currentStepValue,
        stepKey: this.stepManager.getCurrentStepKey(),
        stepDefinition: this.stepManager.getCurrentStepDefinition(),
        totalSteps: this.stepManager.getTotalSteps(),
      });
    }
  }

  // Update selected topics display
  updateSelectedTopicsDisplay() {
    const topicsContainer = this.element.querySelector(
      "[data-selected-topics]"
    );
    if (!topicsContainer) return;

    // Get checked topics from all checkboxes, including hidden ones
    let checkedTopics = Array.from(
      this.element.querySelectorAll('input[name="job[topics][]"]:checked')
    ).map((input) => input.value);

    // Also check for other topic field
    const otherTopicField = this.element.querySelector(
      'input[name="job[other_topic]"]'
    );
    const otherTopic = otherTopicField?.value?.trim();

    if (otherTopic) {
      checkedTopics.push(otherTopic);
    }

    // If no topics found from checkboxes (e.g., step is hidden), use stored topics
    if (checkedTopics.length === 0) {
      checkedTopics = this.getStoredSelectedTopics();
    } else {
      // Store the selected topics for persistence across step changes
      this.storeSelectedTopics(checkedTopics);
    }

    if (checkedTopics.length > 0) {
      topicsContainer.innerHTML = checkedTopics
        .map(
          (topic) =>
            `<span class="inline-block px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full mr-2 mb-1">${topic}</span>`
        )
        .join("");
    } else {
      topicsContainer.innerHTML =
        '<span class="text-gray-500 text-sm">No topics selected</span>';
    }
  }

  // Store selected topics for persistence across step changes
  storeSelectedTopics(topics) {
    // Store in a data attribute on the form element for persistence
    this.element.dataset.selectedTopics = JSON.stringify(topics);
  }

  // Get stored selected topics
  getStoredSelectedTopics() {
    try {
      const stored = this.element.dataset.selectedTopics;
      return stored ? JSON.parse(stored) : [];
    } catch (e) {
      console.warn("Error parsing stored topics:", e);
      return [];
    }
  }

  // Validation methods
  validateCurrentStep() {
    return this.stepManager.validateCurrentStep();
  }

  validateAllSteps() {
    let allValid = true;
    const totalSteps = this.stepManager.getTotalSteps();

    for (let i = 0; i < totalSteps; i++) {
      if (!this.stepManager.validateStepByIndex(i)) {
        allValid = false;
      }
    }

    return allValid;
  }

  // Handle category change
  handleCategoryChange(event) {
    const category = event.target.value;

    // Update visual state of radio card buttons
    this.updateCategoryCardSelection(event.target);

    if (this.stepManager) {
      this.stepManager.handleCategoryChange(category);

      this.dispatch("categoryChanged", {
        detail: {
          category: category,
          totalSteps: this.stepManager.getTotalSteps(),
        },
      });

      // Also dispatch a global event for the vertical progress stepper
      this.dispatchGlobalEvent("form-wizard:categoryChanged", {
        category: category,
        totalSteps: this.stepManager.getTotalSteps(),
      });
    }
  }

  // Initialize category card selection on page load
  initializeCategoryCardSelection() {
    const selectedRadio = this.element.querySelector(
      'input[name="job[job_category]"]:checked'
    );
    if (selectedRadio) {
      this.updateCategoryCardSelection(selectedRadio);
    }

    // Add click event listeners to category card labels to ensure they work
    this.setupCategoryCardClickHandlers();
  }

  // Setup click handlers for category card labels
  setupCategoryCardClickHandlers() {
    const categoryRadios = this.element.querySelectorAll(
      'input[name="job[job_category]"]'
    );

    categoryRadios.forEach((radio) => {
      const label = radio.closest("label");
      if (label) {
        label.addEventListener("click", (event) => {
          if (!radio.disabled) {
            // Ensure the radio button gets checked
            radio.checked = true;

            // Trigger the change event manually
            const changeEvent = new Event("change", { bubbles: true });
            radio.dispatchEvent(changeEvent);

            // Update visual state
            this.updateCategoryCardSelection(radio);

            // Call the category change handler
            this.handleCategoryChange({ target: radio });
          }
        });
      }
    });
  }

  // Update visual state of category radio card buttons
  updateCategoryCardSelection(selectedRadio) {
    // Find all category radio buttons and their labels
    const categoryRadios = this.element.querySelectorAll(
      'input[name="job[job_category]"]'
    );

    categoryRadios.forEach((radio) => {
      const label = radio.closest("label");
      const checkIcon = label.querySelector("svg");
      const focusRing = label.querySelector('span[aria-hidden="true"]');

      if (radio === selectedRadio) {
        // Selected state
        label.classList.remove("border-stone-300");
        label.classList.add("border-[#6100FF]", "ring-2", "ring-[#6100FF]");
        checkIcon.classList.remove("opacity-0");
        checkIcon.classList.add("opacity-100");
        if (focusRing) {
          focusRing.classList.remove("border-transparent");
          focusRing.classList.add("border-[#6100FF]");
        }
      } else {
        // Unselected state
        label.classList.remove("border-[#6100FF]", "ring-2", "ring-[#6100FF]");
        label.classList.add("border-stone-300");
        checkIcon.classList.remove("opacity-100");
        checkIcon.classList.add("opacity-0");
        if (focusRing) {
          focusRing.classList.remove("border-[#6100FF]");
          focusRing.classList.add("border-transparent");
        }
      }
    });
  }

  // Initialize goal card selection on page load
  initializeGoalCardSelection() {
    const selectedRadio = this.element.querySelector(
      'input[name="job[outcome]"]:checked'
    );
    if (selectedRadio) {
      this.updateGoalCardSelection(selectedRadio);
    }

    // Add click event listeners to goal card labels to ensure they work
    this.setupGoalCardClickHandlers();
  }

  // Setup click handlers for goal card labels
  setupGoalCardClickHandlers() {
    const goalRadios = this.element.querySelectorAll(
      'input[name="job[outcome]"]'
    );

    goalRadios.forEach((radio) => {
      const label = radio.closest("label");
      if (label) {
        label.addEventListener("click", (event) => {
          if (radio.disabled) {
            return;
          }

          // Ensure the radio button gets checked
          radio.checked = true;

          // Trigger the change event manually
          const changeEvent = new Event("change", { bubbles: true });
          radio.dispatchEvent(changeEvent);

          // Update visual state
          this.updateGoalCardSelection(radio);

          // Call the goal change handler
          this.handleGoalChange({ target: radio });
        });
      }
    });
  }

  // Update visual state of goal radio card buttons
  updateGoalCardSelection(selectedRadio) {
    // Find all goal radio buttons and their labels
    const goalRadios = this.element.querySelectorAll(
      'input[name="job[outcome]"]'
    );

    goalRadios.forEach((radio) => {
      const label = radio.closest("label");
      const checkIcon = label.querySelector("svg");
      const focusRing = label.querySelector('span[aria-hidden="true"]');

      if (radio === selectedRadio) {
        // Selected state
        label.classList.remove("border-stone-300");
        label.classList.add("border-[#6100FF]", "ring-2", "ring-[#6100FF]");
        checkIcon.classList.remove("opacity-0");
        checkIcon.classList.add("opacity-100");
        if (focusRing) {
          focusRing.classList.remove("border-transparent");
          focusRing.classList.add("border-[#6100FF]");
        }
      } else {
        // Unselected state
        label.classList.remove("border-[#6100FF]", "ring-2", "ring-[#6100FF]");
        label.classList.add("border-stone-300");
        checkIcon.classList.remove("opacity-100");
        checkIcon.classList.add("opacity-0");
        if (focusRing) {
          focusRing.classList.remove("border-[#6100FF]");
          focusRing.classList.add("border-transparent");
        }
      }
    });
  }

  // Handle goal change for social media steps
  handleGoalChange(event) {
    const goalValue = event.target.value;

    // Update visual state of goal radio card buttons
    this.updateGoalCardSelection(event.target);
    const goalDetailsSection = this.element.querySelector(
      "#social-media-goal-details"
    );

    if (goalDetailsSection) {
      const shouldShow = goalValue === "leads" || goalValue === "booked_calls";
      goalDetailsSection.classList.toggle("hidden", !shouldShow);

      // Update required status of conditional fields
      const conditionalFields = goalDetailsSection.querySelectorAll(
        "[data-goal-dependent-required]"
      );
      conditionalFields.forEach((field) => {
        field.required = shouldShow;
        if (!shouldShow) {
          // Clear validation errors when hiding fields
          field.classList.remove("border-red-500");
          const errorElement =
            field.parentElement.querySelector(".error-message");
          if (errorElement) {
            errorElement.classList.add("hidden");
          }
        }
      });
    }
  }

  // Form submission
  submitForm(event) {
    // Let the step manager handle validation and submission
    if (!this.stepManager.submitForm()) {
      event?.preventDefault();

      this.dispatch("validationFailed", {
        detail: {
          errors: this.stepManager.validator.getErrors(),
        },
      });
    } else {
      this.dispatch("formSubmitted", {
        detail: {
          category: this.stepManager.getSelectedCategory(),
          totalSteps: this.stepManager.getTotalSteps(),
        },
      });
    }
  }

  // Utility methods for external access
  getCurrentStep() {
    return this.stepManager ? this.stepManager.getCurrentStepIndex() : 0;
  }

  getCurrentStepKey() {
    return this.stepManager ? this.stepManager.getCurrentStepKey() : null;
  }

  getTotalSteps() {
    return this.stepManager ? this.stepManager.getTotalSteps() : 0;
  }

  getSelectedCategory() {
    return this.stepManager ? this.stepManager.getSelectedCategory() : null;
  }

  isFirstStep() {
    return this.stepManager ? this.stepManager.isFirstStep() : true;
  }

  isLastStep() {
    return this.stepManager ? this.stepManager.isLastStep() : false;
  }

  // Reset the wizard
  reset() {
    if (this.stepManager) {
      this.stepManager.reset();
      this.currentStepValue = 0;

      this.dispatch("wizardReset", {
        detail: {
          totalSteps: this.stepManager.getTotalSteps(),
        },
      });
    }
  }

  // Helper method to dispatch global events
  dispatchGlobalEvent(eventName, detail) {
    const event = new CustomEvent(eventName, {
      detail: detail,
      bubbles: true,
      cancelable: true,
    });
    document.dispatchEvent(event);
  }

  // Value change handlers
  currentStepValueChanged() {
    if (
      this.stepManager &&
      this.stepManager.getCurrentStepIndex() !== this.currentStepValue
    ) {
      this.stepManager.goToStep(this.currentStepValue);
    }
  }

  validateOnNextValueChanged() {
    if (this.stepManager) {
      this.stepManager.options.validateOnNext = this.validateOnNextValue;
    }
  }

  showProgressValueChanged() {
    if (this.stepManager) {
      this.stepManager.options.showProgress = this.showProgressValue;
      this.stepManager.updateUI();
    }
  }

  // Debug methods (can be removed in production)
  debug() {
    if (this.stepManager) {
      console.log("Form Wizard Debug Info:", {
        currentStep: this.stepManager.getCurrentStepIndex(),
        currentStepKey: this.stepManager.getCurrentStepKey(),
        selectedCategory: this.stepManager.getSelectedCategory(),
        totalSteps: this.stepManager.getTotalSteps(),
        isFirstStep: this.stepManager.isFirstStep(),
        isLastStep: this.stepManager.isLastStep(),
        errors: this.stepManager.validator.getErrors(),
      });
    }
  }

  // Setup event listeners for development preview
  setupDevPreviewListeners() {
    if (!this.hasDevPreviewTarget) return;

    // Listen for changes on all form inputs
    this.element.addEventListener("input", () => this.updateDevPreview());
    this.element.addEventListener("change", () => this.updateDevPreview());

    // Initial update
    setTimeout(() => this.updateDevPreview(), 100);
  }

  // Update development preview with current form data
  updateDevPreview() {
    if (!this.hasDevPreviewTarget) return;

    const formData = new FormData(this.element);
    const fields = [
      "job_category",
      "platform",
      "outcome",
      "newsletter_frequency",
      "newsletter_length",
      "lead_magnet_type",
      "budget_range",
      "work_duration",
      "title",
      "description",
      "social_media_goal_type",
      "social_media_understands_risk_acknowledged",
    ];

    // Update topics separately since it's an array
    const topicElements = this.element.querySelectorAll(
      'input[name="job[topics][]"]:checked'
    );
    const topics = Array.from(topicElements).map((el) => el.value);
    const otherTopic = this.element.querySelector(
      'input[name="job[other_topic]"]'
    )?.value;
    if (otherTopic) topics.push(otherTopic);

    const topicsSpan = this.devPreviewTarget.querySelector(
      '[data-dev-field="topics"]'
    );
    if (topicsSpan) {
      topicsSpan.textContent = topics.length > 0 ? topics.join(", ") : "-";
    }

    // Update other fields
    fields.forEach((field) => {
      const span = this.devPreviewTarget.querySelector(
        `[data-dev-field="${field}"]`
      );
      if (span) {
        const value = formData.get(`job[${field}]`) || "-";
        span.textContent = value;
      }
    });

    // Update outcome field status
    this.updateOutcomeFieldStatus();
  }

  // Update the status of outcome fields for debugging
  updateOutcomeFieldStatus() {
    if (!this.hasDevPreviewTarget) return;

    const outcomeFields = this.element.querySelectorAll(
      'select[name="job[outcome]"], select[name="job[outcome_disabled]"], input[name="job[outcome]"], input[name="job[outcome_disabled]"]'
    );

    // Check social media outcome field
    const socialMediaField = this.element.querySelector(
      '[data-step-name="social_media_goal"] select[name="job[outcome]"], [data-step-name="social_media_goal"] select[name="job[outcome_disabled]"], [data-step-name="social_media_goal"] input[name="job[outcome]"]:checked, [data-step-name="social_media_goal"] input[name="job[outcome_disabled]"]:checked'
    );
    const socialMediaStatus = this.devPreviewTarget.querySelector(
      '[data-dev-field="outcome_social_media_status"]'
    );
    if (socialMediaStatus) {
      if (socialMediaField) {
        const isEnabled =
          !socialMediaField.disabled &&
          socialMediaField.name === "job[outcome]";
        socialMediaStatus.textContent = isEnabled
          ? `Enabled (${socialMediaField.value || "empty"})`
          : "Disabled";
        socialMediaStatus.className = isEnabled
          ? "text-green-600"
          : "text-red-600";
      } else {
        socialMediaStatus.textContent = "Not found";
      }
    }

    // Check newsletter outcome field
    const newsletterField = this.element.querySelector(
      '[data-step-name="newsletter_goal"] select[name="job[outcome]"], [data-step-name="newsletter_goal"] select[name="job[outcome_disabled]"], [data-step-name="newsletter_goal"] input[name="job[outcome]"]:checked, [data-step-name="newsletter_goal"] input[name="job[outcome_disabled]"]:checked'
    );
    const newsletterStatus = this.devPreviewTarget.querySelector(
      '[data-dev-field="outcome_newsletter_status"]'
    );
    if (newsletterStatus) {
      if (newsletterField) {
        const isEnabled =
          !newsletterField.disabled && newsletterField.name === "job[outcome]";
        newsletterStatus.textContent = isEnabled
          ? `Enabled (${newsletterField.value || "empty"})`
          : "Disabled";
        newsletterStatus.className = isEnabled
          ? "text-green-600"
          : "text-red-600";
      } else {
        newsletterStatus.textContent = "Not found";
      }
    }

    // Check lead magnet outcome field
    const leadMagnetField = this.element.querySelector(
      '[data-step-name="lead_magnet_goal"] select[name="job[outcome]"], [data-step-name="lead_magnet_goal"] select[name="job[outcome_disabled]"], [data-step-name="lead_magnet_goal"] input[name="job[outcome]"]:checked, [data-step-name="lead_magnet_goal"] input[name="job[outcome_disabled]"]:checked'
    );
    const leadMagnetStatus = this.devPreviewTarget.querySelector(
      '[data-dev-field="outcome_lead_magnet_status"]'
    );
    if (leadMagnetStatus) {
      if (leadMagnetField) {
        const isEnabled =
          !leadMagnetField.disabled && leadMagnetField.name === "job[outcome]";
        leadMagnetStatus.textContent = isEnabled
          ? `Enabled (${leadMagnetField.value || "empty"})`
          : "Disabled";
        leadMagnetStatus.className = isEnabled
          ? "text-green-600"
          : "text-red-600";
      } else {
        leadMagnetStatus.textContent = "Not found";
      }
    }

    // Update main outcome status
    const outcomeStatus = this.devPreviewTarget.querySelector(
      '[data-dev-field="outcome_status"]'
    );
    if (outcomeStatus) {
      const enabledFields = Array.from(outcomeFields).filter(
        (field) => !field.disabled && field.name === "job[outcome]"
      );
      if (enabledFields.length === 0) {
        outcomeStatus.textContent = "(No enabled outcome fields!)";
        outcomeStatus.className = "text-red-500 font-bold";
      } else if (enabledFields.length > 1) {
        outcomeStatus.textContent = `(${enabledFields.length} enabled fields - conflict!)`;
        outcomeStatus.className = "text-red-500 font-bold";
      } else {
        outcomeStatus.textContent = "(1 enabled field)";
        outcomeStatus.className = "text-green-600";
      }
    }
  }
}
