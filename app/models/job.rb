# == Schema Information
#
# Table name: jobs
#
#  id                                         :bigint           not null, primary key
#  application_deadline                       :datetime
#  budget_range                               :integer
#  business_challenge                         :text
#  charge_per_client                          :string
#  client_count                               :string
#  description                                :text
#  emulated_brands_description                :text
#  expires_at                                 :datetime
#  involvement_level                          :string
#  is_premium                                 :boolean          default(FALSE)
#  job_category                               :integer
#  lead_magnet_type                           :string
#  newsletter_frequency                       :string
#  newsletter_length                          :string
#  notification_preference                    :integer
#  offer_summary                              :text
#  outcome                                    :integer
#  payment_frequency                          :integer
#  platform                                   :integer
#  published_at                               :datetime
#  requirements                               :text
#  social_media_goal_type                     :string
#  social_media_understands_risk_acknowledged :boolean
#  status                                     :string           default("draft")
#  target_audience_description                :text
#  title                                      :string
#  topics                                     :string           default([]), is an Array
#  useful_info                                :text
#  work_duration                              :string
#  created_at                                 :datetime         not null
#  updated_at                                 :datetime         not null
#  organization_id                            :bigint           not null
#
# Indexes
#
#  index_jobs_on_organization_id  (organization_id)
#
# Foreign Keys
#
#  fk_rails_...  (organization_id => organizations.id)
#
class Job < ApplicationRecord
  include JobFormConfiguration

  # Predefined list of job topics
  AVAILABLE_TOPICS = [
    'Artificial Intelligence',
    'E-Commerce & Dropshipping',
    'General Business',
    'Health & Wellness',
    'Info Products',
    'Investing & Finance',
    'Masculinity',
    'Marketing & Sales',
    'Productivity',
    'Real Estate',
    'SaaS',
    'Self-Development',
    'SEO',
    'Social Media',
    'Spirituality',
    'Tech & Startups',
    'Web 3',
    'Writing'
  ].freeze

  belongs_to :organization

  # Add these associations if they don't exist
  # The :job_applications and :applicants associations were defined again below.
  # Consolidating them to the later definitions which include `dependent: :destroy` for job_applications.

  # Ensure searchkick is called only once
  unless respond_to?(:searchkick_index)
    searchkick word_start: %i[title description topics],
               filterable: %i[
                 is_premium
                 topics
               job_category
               platform
               budget_range
               status
               outcome
               ]
  end

  enum :outcome, {
    followers: 0,
    leads: 1,
    booked_calls: 2,
    email_subscribers: 3,
    authority_brand_awareness: 4,
    media_speaking_engagements: 5,
    build_brand: 6,
    drive_traffic_to_offers: 7,
    nurture_leads_for_future_sales: 8,
    grow_email_list: 9,
    book_sales_calls: 10,
    drive_traffic_to_website: 11
  }

  enum :payment_frequency, { monthly: 0, quarterly: 1, project_based: 2 }

  enum :platform, {
    x_twitter: 0,
    linkedin: 1,
    instagram: 2,
    youtube: 3,
    substack: 4,
    threads: 5,
    not_sure_yet: 6
  }

  enum :job_category, { social_media: 0, lead_magnet: 1, newsletter: 3 }
  enum :budget_range,
       {
         under_1000: 0,
         range_1000_2000: 1,
         range_2000_3500: 2,
         range_3500_5000: 3,
         above_5000: 4
       }

  enum :newsletter_frequency, { daily: 0, a_few_times_a_week: 1, weekly: 2, bi_weekly: 3, monthly: 4 }, suffix: true
  enum :newsletter_length, {
    under_300_words: 0,
    words_300_600: 1,
    words_600_1000_plus: 2,
    any_length_is_fine: 3
  }
  enum :lead_magnet_type, { ebook: 0, checklist: 1, template: 2, webinar: 3, case_study: 4 }

  enum :work_duration, {
    one_time_project: 0,
    short_term: 1,
    long_term: 2,
    not_sure: 3
  }

  enum :involvement_level, { hands_off: 0, collaborative: 1, hands_on: 2 }
  enum :social_media_goal_type, { social_media_leads: 0, booked_calls: 1 }, suffix: true

  has_many :job_applications, dependent: :destroy # Consolidated definition
  has_many :applicants, through: :job_applications, source: :user # Consolidated definition (was duplicated)
  has_many :saved_jobs, dependent: :destroy
  has_many :saved_by_users, through: :saved_jobs, source: :user

  enum :status,
       { draft: 'draft', published: 'published', expired: 'expired' },
       default: 'draft'

  # Validations
  validates :title, presence: true, if: :published?
  validates :description, presence: true, if: :published?
  validates :job_category, presence: true, if: :published?
  validates :budget_range, presence: true, if: :published?
  validates :work_duration, presence: true, if: :published?
  validates :outcome, presence: true, if: :published?

  # Category-specific validations
  validates :platform, presence: true, if: -> { published? && social_media? }
  validates :newsletter_frequency, presence: true, if: -> { published? && newsletter? }
  validates :newsletter_length, presence: true, if: -> { published? && newsletter? }
  validates :lead_magnet_type, presence: true, if: -> { published? && lead_magnet? }

  # Conditional validations for social media goals
  validates :social_media_goal_type, presence: true,
    if: -> { published? && social_media? && (leads? || booked_calls?) }
  validates :social_media_understands_risk_acknowledged, acceptance: true,
    if: -> { published? && social_media? && (leads? || booked_calls?) }

  # Set default application deadline to 30 days from creation if not provided
  before_create :set_default_application_deadline

  # Set published_at when status changes to published
  before_save :set_published_at, if: :will_save_change_to_status?

  def saved_by?(user)
    return false unless user
    saved_jobs.exists?(user: user)
  end

  def search_data
    {
      id: id,
      title: title,
      description: description,
      is_premium: is_premium,
      topics: topics,
      job_category: job_category,
      platform: platform,
      budget_range: budget_range,
      outcome: outcome,
      status: status,
      created_at: created_at,
      newsletter_frequency: newsletter_frequency,
      newsletter_length: newsletter_length,
      lead_magnet_type: lead_magnet_type,
      work_duration: work_duration,
      target_audience_description: target_audience_description,
      emulated_brands_description: emulated_brands_description,
      involvement_level: involvement_level,
      social_media_goal_type: social_media_goal_type,
      social_media_understands_risk_acknowledged: social_media_understands_risk_acknowledged,
    }
  end

  # NOTE: Removed the random premium? method as it conflicts with the actual is_premium field.
  # def premium?
  #   [true, false].sample
  # end

  # Add this method to your Job model
  def applicants_count
    # If you have job applications associated with this job
    job_applications.count

    # If the above doesn't work, you might need to adjust based on your actual associations
  end

  def salary_range
    case budget_range&.to_sym
    when :under_1000
      "Under $1,000 USD per month (ghostwriters with no past clients)"
    when :range_1000_2000
      "$1,000-$2,000 USD per month (ghostwriters with a bit of experience)"
    when :range_2000_3500
      "$2,000-$3,500 USD per month (ghostwriters with solid experience)"
    when :range_3500_5000
      "$3,500-$5,000 USD per month (ghostwriters with high experience)"
    when :above_5000
      "$5,000+ USD per month (top-tier ghostwriters with highly specialized offers)"
    else
      'Not specified'
    end
  end

  # Check if the job is active (published)
  def active?
    status == 'published'
  end

  private

  # Set the application deadline to 30 days from creation if not provided
  def set_default_application_deadline
    self.application_deadline ||= 30.days.from_now
  end

  # Set published_at timestamp when status changes to published
  def set_published_at
    if status == 'published' && published_at.blank?
      self.published_at = Time.current
    elsif status != 'published'
      self.published_at = nil
    end
  end
end
