class ApplicationController < ActionController::Base
  include Pagy::Backend
  before_action :set_current_request_details
  before_action :authenticate
  before_action :check_verification
  before_action :require_onboarding_completion
  before_action :set_current_organization
  before_action :require_organization_selected # Remove :logout since it doesn't exist

  private

  def require_onboarding_completion
    return unless Current.user
    return if controller_path.start_with?('identity/')
    return if controller_name == 'onboarding'
    return if controller_name == 'registrations'

    # Allow access to launchpad regardless of onboarding state
    return if controller_name == 'launchpad'

    # Determine if access should be granted based on completed onboarding
    onboarding_ok =
      if Current.user.onboarding_completed?
        # General (Scout) onboarding done?
        true
      elsif controller_path.start_with?('talent/') &&
            Current.user.talent_signup_completed?
        # Accessing Talent area and Talent signup done?
        true
      else
        false # Neither general nor specific onboarding is complete for this area
      end

    return if onboarding_ok # Allow access if onboarding is considered complete for the context

    # If onboarding is not ok, redirect to the appropriate step
    # (This redirect logic assumes 'personal' is always the first incomplete step if general onboarding isn't done)
    redirect_to(
      if Current.user.onboarding_step == 'personal'
        onboarding_personal_path
      else
        # If step isn't personal, but onboarding isn't complete, implies org step is needed (for Scouts)
        onboarding_organization_path
      end,
      alert: 'Please complete your profile setup to continue',
    )
  end

  def check_verification
    return unless Current.user
    return if controller_path.start_with?('identity/')
    return if controller_name == 'registrations'

    if !Current.user.verified?
      sign_out
      redirect_to sign_in_path,
                  alert: 'Please verify your email before continuing'
    end
  end

  def authenticate
    if session_record = Session.find_by_id(cookies.signed[:session_token])
      Current.session = session_record
      Current.user = session_record.user # Explicitly assign/reload Current.user
    else
      redirect_to sign_in_path
    end
  end # Added missing end

  def sign_out
    cookies.delete(:session_token)
    Current.session&.destroy
  end

  def set_current_request_details
    Current.user_agent = request.user_agent
    Current.ip_address = request.ip
  end

  # Add these methods to your ApplicationController
  # (You'll need to integrate this with your existing code)

  def set_current_organization
    return unless Current.user

    # 1. Prioritize last logged-in organization
    if Current.user.last_logged_in_organization_id
      org =
        Current.user.organizations.find_by(
          id: Current.user.last_logged_in_organization_id,
        )
      if org
        Current.organization = org
        session[:organization_id] = org.id
        return # Found the org, no need for further checks
      else
        # Last logged-in org is invalid (e.g., user removed), clear the stored ID
        # Use update_column to skip validations/callbacks if necessary, though update should be fine here.
        Current.user.update(last_logged_in_organization_id: nil)
      end
    end

    # 2. Check session if last logged-in org wasn't found or set
    if session[:organization_id]
      org = Current.user.organizations.find_by(id: session[:organization_id])
      if org
        Current.organization = org

        # No need to set session[:organization_id] again, it's already there
        return # Found the org, no need for further checks
      else
        # Session org is invalid, clear it
        session.delete(:organization_id)
      end
    end

    # 3. Auto-set if user has exactly one org and none is selected yet
    if Current.organization.nil? && Current.user.organizations.count == 1
      org = Current.user.organizations.first
      Current.organization = org
      session[:organization_id] = org.id
    end
    # (If multiple orgs and none selected via last_logged_in or session, Current.organization remains nil)
  end

  def require_organization_selected
    return unless Current.user

    # Only enforce organization selection within the Scout namespace
    return unless controller_path.start_with?('scout/')

    return if Current.organization.present? # already selected

    redirect_to organizations_path, alert: 'Please choose an organization'
  end
end
