module Scout
  class JobsController < Scout::BaseController
    before_action :set_job, only: %i[show edit update destroy duplicate preview]

    def index
      @jobs = current_organization.jobs.order(created_at: :desc)

      # Count jobs by status if the column exists
      if Job.column_names.include?('status')
        @published_jobs_count = current_organization.jobs.published.count
        @draft_jobs_count = current_organization.jobs.draft.count
      else
        @published_jobs_count = 0
        @draft_jobs_count = 0
      end
      @total_jobs_count = current_organization.jobs.count

      # Filter by search term if provided
      if params[:search].present?
        @jobs =
          @jobs.where(
            'title ILIKE ? OR description ILIKE ?',
            "%#{params[:search]}%",
            "%#{params[:search]}%",
          )
      end

      # Filter by status if provided and column exists
      if params[:status].present? && Job.column_names.include?('status')
        @jobs = @jobs.where(status: params[:status].to_sym)
      end
    end

    def show; end

    def new
      @job = current_organization.jobs.new
    end

    def create
      # Get job params and handle other_topic separately
      job_attributes = job_params
      other_topic = params[:job][:other_topic]

      @job = current_organization.jobs.new(job_attributes)

      # Handle other_topic if present
      if other_topic.present?
        @job.topics = Array(@job.topics) + [other_topic]
      end

      # Determine status based on which submit button was clicked
      case params[:commit]
      when 'Post Job'
        @job.status = 'published'
        @job.published_at = Time.current
      when 'Save as Draft'
        @job.status = 'draft'
      else
        @job.status = 'draft' # Default to draft
      end

      if @job.save
        respond_to do |format|
          if @job.published?
            format.html { redirect_to scout_jobs_path, notice: 'Job was successfully posted and is now live!' }
            format.turbo_stream { redirect_to scout_jobs_path, notice: 'Job was successfully posted and is now live!' }
          else
            format.html { redirect_to scout_jobs_path, notice: 'Job was successfully saved as draft.' }
            format.turbo_stream { redirect_to scout_jobs_path, notice: 'Job was successfully saved as draft.' }
          end
        end
      else

        respond_to do |format|
          format.html { render :new, status: :unprocessable_entity }
          format.turbo_stream {
            render turbo_stream: turbo_stream.replace("new_job_multi_step",
              partial: "scout/jobs/form", locals: { job: @job }),
              status: :unprocessable_entity
          }
        end
      end
    end

    def edit; end

    def update
      # Get the job params
      job_attributes = job_params

      # Handle other_topic if present
      if params[:job][:other_topic].present?
        # Ensure topics is an array
        topics = Array(job_attributes[:topics] || @job.topics)

        # Add the other topic
        topics << params[:job][:other_topic]

        # Update the job_attributes
        job_attributes[:topics] = topics
      end

      # Handle status changes based on submit button
      case params[:commit]
      when 'Post Job'
        job_attributes[:status] = 'published'
        job_attributes[:published_at] = Time.current if @job.draft?
      when 'Save as Draft'
        job_attributes[:status] = 'draft'
      end

      if @job.update(job_attributes)
        if @job.published?
          redirect_to scout_job_path(@job), notice: 'Job was successfully posted and is now live!'
        else
          redirect_to scout_job_path(@job), notice: 'Job was successfully updated.'
        end
      else
        render :edit
      end
    end

    def destroy
      Rails.logger.info("Attempting to destroy job with ID: #{@job.id}")
      @job.destroy
      Rails.logger.info("Job with ID: #{@job.id} destroyed successfully.")
      redirect_to scout_jobs_path, notice: 'Job was successfully deleted.'
    end

    def duplicate
      new_job = @job.dup
      new_job.title = "Copy of #{@job.title}"
      new_job.status = 'draft'
      new_job.created_at = Time.current

      if new_job.save
        redirect_to edit_scout_job_path(new_job),
                    notice: 'Job was successfully duplicated.'
      else
        redirect_to scout_jobs_path, alert: 'Failed to duplicate job.'
      end
    end

    def preview
      render :preview
    end

    private

    def set_job
      @job = current_organization.jobs.find(params[:id])
    end

    def job_params
      permitted_params = params
        .require(:job)
        .permit(
          :title,
          :description,
          :requirements,
          :status,
          :location,
          :job_type,
          :salary_range,
          :application_deadline,
          :job_category,
          :outcome,
          :platform,
          { topics: [] },
          :budget_range,
          :client_count,
          :charge_per_client,
          :business_challenge,
          :useful_info,
          :offer_summary,
          :newsletter_frequency,
          :newsletter_length,
          :lead_magnet_type,
          :work_duration,
          :target_audience_description,
          :emulated_brands_description,
          :involvement_level,
          :social_media_goal_type,
          :social_media_understands_risk_acknowledged,
        )

      # Explicitly remove other_topic if it somehow got through
      permitted_params.delete(:other_topic)
      permitted_params
    end
  end
end