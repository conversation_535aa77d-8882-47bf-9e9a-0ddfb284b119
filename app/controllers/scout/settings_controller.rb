module Scout
  class SettingsController < Scout::BaseController
    def show
    end

    def edit
    end

    def update
      if @settings.update(settings_params)
        redirect_to settings_path, notice: "Settings updated"
      else
        render :edit
      end
    end

    private
      def settings_params
        params.require(:settings).permit(:email, :name, :bio, :location, :website, :twitter, :github, :linkedin, :instagram, :youtube, :resume)
      end
  end
end
