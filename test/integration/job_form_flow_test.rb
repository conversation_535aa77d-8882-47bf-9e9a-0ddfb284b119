require 'test_helper'

class JobFormFlowTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data first
    Job.destroy_all # Though this test file might not create jobs directly, good practice if related flows do.
    OrganizationMembership.destroy_all
    User.destroy_all
    Organization.destroy_all

    # Create a test user with scout privileges
    @user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Test',
        last_name: 'Scout',
        time_zone: 'UTC',
        onboarding_completed: true,
        onboarding_step: 'completed',
        signup_intent: 'scout',
        scout_signup_completed: true,
        talent_signup_completed: false,
      )

    # Create an organization for the scout
    @organization = Organization.create!(name: 'Test Organization')

    # Add scout to organization
    OrganizationMembership.create!(
      user: @user,
      organization: @organization,
      org_role: 'admin',
    )

    @user.update!(last_logged_in_organization_id: @organization.id)
  end

  def teardown
    # Clean up test data
    # Destroy specific instances created in setup to ensure callbacks run
    @user.destroy if @user&.persisted?
    @organization.destroy if @organization&.persisted?
    # Fallback if needed, but specific destruction is preferred for clarity
    # OrganizationMembership.destroy_all
    # User.destroy_all
    # Organization.destroy_all
  end

  test 'can access job creation form after login' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'

    # Access job creation form
    get new_scout_job_url
    assert_response :success

    # Check that the form contains the expected elements
    assert_select 'form#new_job_multi_step'
    assert_select "[data-controller='form-wizard']"
    assert_select "select[name='job[job_category]']"
    assert_select 'button', text: 'Continue'
  end

  test 'form has correct step structure' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }

    # Access job creation form
    get new_scout_job_url
    assert_response :success

    # Check for category selection step
    assert_select "[data-step-name='category_selection']"

    # Check for topics selection step
    assert_select "[data-step-name='topics_selection']"

    # Check for newsletter steps
    assert_select "[data-step-name='newsletter_goal']"
    assert_select "[data-step-name='newsletter_frequency']"
    assert_select "[data-step-name='newsletter_length']"

    # Check for job details step
    assert_select "[data-step-name='job_details']"
  end

  test 'newsletter steps have correct content' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }

    # Access job creation form
    get new_scout_job_url
    assert_response :success

    # Check newsletter goal step
    assert_select "[data-step-name='newsletter_goal']" do
      assert_select 'h3', text: 'Newsletter Goal'
      assert_select 'label', text: /What's the main goal for your newsletter?/
      assert_select "select[name='job[outcome]']"
      assert_select '[data-selected-topics]' # Topics display area
    end

    # Check newsletter frequency step
    assert_select "[data-step-name='newsletter_frequency']" do
      assert_select 'h3', text: 'Newsletter Frequency'
      assert_select 'label',
                    text: /How often do you want to send your newsletter?/
      assert_select "input[name='job[newsletter_frequency]'][type='radio']"
    end

    # Check newsletter length step
    assert_select "[data-step-name='newsletter_length']" do
      assert_select 'h3', text: 'Newsletter Length'
      assert_select 'label', text: /How long should each newsletter be?/
      assert_select "input[name='job[newsletter_length]'][type='radio']"
    end
  end

  test 'form has correct JavaScript controllers' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }

    # Access job creation form
    get new_scout_job_url
    assert_response :success

    # Check that form has form-wizard controller
    assert_select "form[data-controller='form-wizard']"

    # Check that category select has correct action
    assert_select "select[name='job[job_category]'][data-action*='form-wizard#handleCategoryChange']"

    # Check navigation buttons
    assert_select "button[data-form-wizard-target='nextButton']"
    assert_select "button[data-form-wizard-target='previousButton']"
  end

  test 'steps are initially hidden except first one' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }

    # Access job creation form
    get new_scout_job_url
    assert_response :success

    # Category selection should be visible
    assert_select "[data-step-name='category_selection']:not(.hidden)"

    # Topics selection should be hidden
    assert_select "[data-step-name='topics_selection'].hidden"

    # Newsletter steps should be hidden
    assert_select "[data-step-name='newsletter_goal'].hidden"
    assert_select "[data-step-name='newsletter_frequency'].hidden"
    assert_select "[data-step-name='newsletter_length'].hidden"

    # Job details should be hidden
    assert_select "[data-step-name='job_details'].hidden"
  end
end
